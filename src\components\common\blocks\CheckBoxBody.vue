<template>
  <div class="q-ml-md">
    <!-- รายการตัวเลือก (Drag Indicator, Checkbox, Text Field) -->
    <div
      v-for="(choice, index) in store.checkboxOptions"
      :key="index"
      class="row items-center q-mb-sm draggable-row"
      @dragover.prevent
      @drop="store.drop(index, $event, true)"
      :style="{ transition: 'all 0.3s ease', opacity: store.draggedIndex === index ? 0.5 : 1 }"
    >
      <q-btn
        flat
        round
        icon="drag_indicator"
        color="grey"
        @mousedown="store.startDrag(index)"
        draggable="true"
        @dragstart="store.handleDragStart($event)"
        @dragend="store.endDrag"
        class="q-mr-sm"
        @mouseover="store.hoverRow(index)"
      />
      <q-checkbox
        v-model="store.checkboxSelectedOptions"
        :val="store.checkboxOptions[index]!.value"
        color="primary"
        disable
        class="q-mr-sm"
      />
      <div class="column full-width">
        <div class="row items-center q-col-gutter-sm">
          <!-- Option text input -->
          <div class="col-8">
            <q-input
              v-model="store.checkboxOptions[index]!.optionText"
              :placeholder="store.checkboxOptions[index]!.placeholder"
              dense
              @input="handleOptionTextChange(index, $event)"
              @blur="handleOptionTextBlur(index)"
              class="option-text-input"
            />
          </div>
          <!-- Value input -->
          <div class="col-2">
            <q-input
              v-model.number="store.checkboxOptions[index]!.score"
              type="number"
              dense
              class="value-input"
              @input="updateOptionValue(index)"
            />
          </div>
        </div>
      </div>
      <q-btn flat round icon="image" color="grey" class="q-ml-sm" />
      <q-btn
        v-if="store.checkboxOptions.length > 1"
        flat
        round
        icon="close"
        @click="store.removeOption(index, true)"
        :disable="store.checkboxOptions.length <= 1"
        class="q-ml-sm"
      />
    </div>

    <!-- ปุ่มเพิ่มช้อย -->
    <div class="row items-center q-mt-sm">
      <q-btn
        flat
        color="secondary"
        label="เพิ่มตัวเลือก"
        icon="add"
        @click="handleAddOption()"
        :loading="isCreatingOption"
        :disable="isCreatingOption"
      />
      <q-btn
        flat
        color="secondary"
        label="เพิ่ม 'อื่นๆ'"
        icon="add"
        @click="handleAddOtherOption()"
        :loading="isCreatingOption"
        :disable="isCreatingOption"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { inject, ref } from 'vue';
import type { ItemBlockStore } from 'src/stores/item_block_store';
import type { ItemBlock } from 'src/types/models';
import { OptionService, type CreateOptionData } from 'src/services/asm/optionService';
import { Notify } from 'quasar';

// Get the store instance provided by the parent ItemBlock
const store = inject<ItemBlockStore>('blockStore');
if (!store) {
  throw new Error('CheckBoxAns must be used within an ItemBlock component');
}

// Get the itemBlock prop to access actual option IDs
const props = defineProps<{
  itemBlock: ItemBlock;
}>();

// Initialize option service
const optionService = new OptionService();

// Loading state for option creation
const isCreatingOption = ref(false);

// Inject auto-save functions from parent ItemBlockComponent
const autoSave = inject<{
  triggerOptionAutoSave: (
    optionId: number,
    field: 'optionText' | 'score',
    value: string | number,
  ) => void;
  isSaving: { value: boolean };
}>('autoSave');

// Handler for option text changes with debounced auto-save
const handleOptionTextChange = (index: number, event: Event) => {
  const target = event.target as HTMLInputElement;
  const newValue = target.value;

  // Update the store immediately for UI responsiveness
  store.updateOption(index, true); // true for checkbox

  // Get the actual option ID from the itemBlock prop
  const actualOption = props.itemBlock.options?.[index];

  // Only trigger auto-save if the option has an ID (exists in backend)
  if (actualOption && actualOption.id && autoSave) {
    // Use the debounced auto-save from parent component
    autoSave.triggerOptionAutoSave(actualOption.id, 'optionText', newValue);
  }
};

// Handler for option text blur (immediate save for important changes)
const handleOptionTextBlur = async (index: number) => {
  const actualOption = props.itemBlock.options?.[index];

  // If option doesn't have an ID yet, it needs to be created first
  if (actualOption && !actualOption.id) {
    const optionText = store.checkboxOptions[index]?.optionText || '';

    // Only create if user has entered some text
    if (optionText.trim()) {
      try {
        const optionData: CreateOptionData = {
          optionText: optionText,
          itemBlockId: props.itemBlock.id,
          value: store.checkboxOptions[index]?.score || 0,
        };

        const createdOption = await optionService.createOption(optionData);

        // Update the itemBlock with the new option ID
        if (props.itemBlock.options && props.itemBlock.options[index]) {
          props.itemBlock.options[index].id = createdOption.id;
          props.itemBlock.options[index].optionText = createdOption.optionText;
          props.itemBlock.options[index].value = createdOption.value;
        }

        console.log('✅ Checkbox option created on blur:', createdOption);
      } catch (error) {
        console.error('❌ Failed to create checkbox option on blur:', error);
      }
    }
  }
};

// Handler for adding new checkbox option
const handleAddOption = async () => {
  if (isCreatingOption.value) return;

  try {
    isCreatingOption.value = true;

    // First add to local store for immediate UI feedback
    store.addOption(true); // true for checkbox

    // Prepare option data for API
    const newOptionIndex = store.checkboxOptions.length - 1;
    const newOption = store.checkboxOptions[newOptionIndex];

    if (!newOption) {
      throw new Error('Failed to create checkbox option in store');
    }

    const optionData: CreateOptionData = {
      optionText: '', // Start with empty string as requested
      itemBlockId: props.itemBlock.id,
      value: newOption.score || 0,
    };

    // Call API to create option
    const createdOption = await optionService.createOption(optionData);

    // Update the itemBlock options array with the new option
    if (createdOption) {
      // Add the new option to the itemBlock's options array
      if (!props.itemBlock.options) {
        props.itemBlock.options = [];
      }

      props.itemBlock.options.push({
        id: createdOption.id,
        itemBlockId: createdOption.itemBlockId,
        optionText: createdOption.optionText,
        value: createdOption.value,
        sequence: createdOption.sequence,
        imagePath: createdOption.imagePath,
        nextSection: createdOption.nextSection,
      });

      // Update the local store with backend data
      if (store.checkboxOptions[newOptionIndex]) {
        store.checkboxOptions[newOptionIndex].optionText = createdOption.optionText;
        store.checkboxOptions[newOptionIndex].score = createdOption.value;
      }
    }

    console.log('✅ Checkbox option created successfully:', createdOption);
  } catch (error) {
    console.error('❌ Failed to create checkbox option:', error);

    // Remove the option from store if API call failed
    if (store.checkboxOptions.length > 1) {
      store.removeOption(store.checkboxOptions.length - 1, true);
    }

    Notify.create({
      type: 'negative',
      message: 'ไม่สามารถเพิ่มตัวเลือกได้ กรุณาลองใหม่อีกครั้ง',
      position: 'top',
    });
  } finally {
    isCreatingOption.value = false;
  }
};

// Handler for adding "other" checkbox option
const handleAddOtherOption = async () => {
  if (isCreatingOption.value) return;

  try {
    isCreatingOption.value = true;

    // First add to local store for immediate UI feedback
    store.addOtherOption(true); // true for checkbox

    // Prepare option data for API
    const newOptionIndex = store.checkboxOptions.length - 1;
    const newOption = store.checkboxOptions[newOptionIndex];

    if (!newOption) {
      throw new Error('Failed to create other checkbox option in store');
    }

    const optionData: CreateOptionData = {
      optionText: '', // Start with empty string as requested
      itemBlockId: props.itemBlock.id,
      value: newOption.score || 0,
    };

    // Call API to create option
    const createdOption = await optionService.createOption(optionData);

    // Update the itemBlock options array with the new option
    if (createdOption) {
      // Add the new option to the itemBlock's options array
      if (!props.itemBlock.options) {
        props.itemBlock.options = [];
      }

      props.itemBlock.options.push({
        id: createdOption.id,
        itemBlockId: createdOption.itemBlockId,
        optionText: createdOption.optionText,
        value: createdOption.value,
        sequence: createdOption.sequence,
        imagePath: createdOption.imagePath,
        nextSection: createdOption.nextSection,
      });

      // Update the local store with backend data
      if (store.checkboxOptions[newOptionIndex]) {
        store.checkboxOptions[newOptionIndex].optionText = createdOption.optionText;
        store.checkboxOptions[newOptionIndex].score = createdOption.value;
      }
    }

    console.log('✅ Other checkbox option created successfully:', createdOption);
  } catch (error) {
    console.error('❌ Failed to create other checkbox option:', error);

    // Remove the option from store if API call failed
    if (store.checkboxOptions.length > 1) {
      store.removeOption(store.checkboxOptions.length - 1, true);
    }

    Notify.create({
      type: 'negative',
      message: 'ไม่สามารถเพิ่มตัวเลือก "อื่นๆ" ได้ กรุณาลองใหม่อีกครั้ง',
      position: 'top',
    });
  } finally {
    isCreatingOption.value = false;
  }
};

// Function to update option value (score)
const updateOptionValue = (index: number) => {
  // Ensure the score is a number
  const score = store.checkboxOptions[index]!.score;
  if (typeof score !== 'number') {
    store.checkboxOptions[index]!.score = Number(score) || 0;
  }

  // Make sure we don't override the updateChoice method's functionality
  store.updateOption(index, true);
};
</script>

<style scoped>
.q-input {
  max-width: 400px;
  width: 400px;
  margin-right: 10px;
}

.draggable-row {
  transition: all 0.3s ease;
}

.draggable-row:hover,
.draggable-row[dragged-index]:hover {
  background-color: #f0f0f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.draggable-row.dragging {
  opacity: 0.5;
}

.option-text-input {
  width: 100%;
}

.value-input {
  width: 100%;
  max-width: 120px;
}

.full-width {
  width: 100%;
}
</style>
