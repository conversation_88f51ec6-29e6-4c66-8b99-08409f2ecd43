import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import type { UpdateSubmissionDto } from '../dto/updates/update-submission.dto';
import type { CreateSubmissionDto } from '../dto/creates/create-submission.dto';
import { StartQuizDto } from '../dto/start-quiz.dto';
import { Submission } from '../entities/submission.entity';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { Assessment } from '../entities/assessment.entity';

@Injectable()
export class SubmissionsService {
  @InjectEntityManager()
  private entityManager: EntityManager;
  @InjectRepository(Submission)
  private submissiobRepository: Repository<Submission>;

  async create(createSubmissionDto: CreateSubmissionDto) {
    const submission = this.submissiobRepository.create({
      assessmentId: createSubmissionDto.assessmentId,
      userId: createSubmissionDto.userId,
      startAt: new Date(),
      endAt: null,
    });

    return await this.submissiobRepository.save(submission); // ← ต้อง save
  }

  findAll() {
    return `This action returns all submissions`;
  }

  findOne(id: number) {
    return `This action returns a #${id} submission`;
  }

  findDraftByAsmIdAndUserId(asmId: number, userId: number) {
    return this.submissiobRepository.findOne({
      where: {
        assessmentId: asmId,
        userId: userId,
        endAt: null,
      },
    });
  }

  update(id: number, updateSubmissionDto: UpdateSubmissionDto) {
    return `This action updates a #${id} submission`;
  }

  remove(id: number) {
    return `This action removes a #${id} submission`;
  }

  async startAssessment(startQuizDto: StartQuizDto) {
    const { linkUrl, userId } = startQuizDto;
    const now = new Date();

    // 🔍 ค้นหา assessment จากลิงก์
    const assessment = await this.entityManager.findOne(Assessment, {
      where: { linkURL: linkUrl },
    });
    if (!assessment) throw new NotFoundException('Assessment not found');

    // ⏰ ตรวจสอบว่า assessment หมดเวลาแล้วหรือไม่
    if (assessment.endAt < now)
      throw new BadRequestException('Assessment is closed');

    // 🔁 ตรวจสอบว่าผู้ใช้เคยเริ่มทำแบบทดสอบนี้ และยังไม่หมดเวลา
    const activeSubmission = await this.entityManager.findOne(Submission, {
      where: {
        assessmentId: assessment.id,
        userId,
        submitAt: null,
      },
    });
    if (activeSubmission && activeSubmission.endAt > now) {
      return activeSubmission;
    }

    // 📊 ตรวจสอบว่าจำนวนการส่งไม่เกิน limit
    if (assessment.submitLimit > 0) {
      const count = await this.entityManager.count(Submission, {
        where: { assessmentId: assessment.id, userId },
      });
      if (count >= assessment.submitLimit) {
        throw new ForbiddenException('Assessment submission limit reached');
      }
    }

    // ✅ สร้าง submission ใหม่
    const submission = this.entityManager.create(Submission, {
      assessmentId: assessment.id,
      userId,
      startAt: now,
      endAt: new Date(now.getTime() + assessment.timeout * 1000),
    });

    return this.entityManager.save(Submission, submission);
  }

  async submitAssessment(submissionId: number) {
    const submission = await this.entityManager.findOne(Submission, {
      where: { id: submissionId },
    });
    if (!submission) {
      throw new NotFoundException('Submission not found');
    }

    // validate submission is not submitted yet
    if (submission.submitAt) {
      throw new BadRequestException('Submission already submitted');
    }
    submission.endAt = new Date();
    submission.submitAt = new Date();
    return this.entityManager.save(Submission, submission);
  }

  async getQuizScore(submissionId: number) {
    const submission = await this.entityManager.findOne(Submission, {
      where: { id: submissionId },
      relations: {
        responses: { question: true, selectedOption: true },
        assessment: true,
      },
    });

    if (!submission) {
      throw new NotFoundException('Submission not found');
    }

    let score = 0;
    for (const response of submission.responses) {
      // if (response.question.score) {
      //   totalScore += response.selectedOption.value;
      // }
      if (response.selectedOption && response.selectedOption.value) {
        // เก็บคะแนนจาก option
        score += response.selectedOption.value;
      }
    }

    // Calculate used time in seconds
    const usedTimeMs =
      submission.endAt.getTime() - submission.startAt.getTime();
    const usedTime = Math.floor(usedTimeMs / 1000);

    // Convert to human-readable format hours:minutes:seconds
    const hours = Math.floor(usedTime / 3600);
    const minutes = Math.floor((usedTime % 3600) / 60);
    const seconds = usedTime % 60;
    const usedTimeFormatted = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

    return {
      score,
      totalScore: submission.assessment.totalScore,
      usedTimeFormatted, // in HH:MM:SS format
      submitTime: submission.endAt,
    };
  }
}
